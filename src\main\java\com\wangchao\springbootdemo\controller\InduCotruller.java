package com.wangchao.springbootdemo.controller;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.wangchao.springbootdemo.entity.User;
import com.wangchao.springbootdemo.util.UserCacheConfig;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/cache")
public class InduCotruller {

    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private Cache<Long, User> userCache;

    @PostMapping("/user/test")
//    @Cacheable(value = "indu1", key = "#user.id")
    @Cached(name="userCache:", key = "#user.id", expire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.BOTH, cacheNullValue = true)
    public User addUser(@RequestBody User user) {
        System.err.println("addUser");
        return user;
    }

   @PostMapping("/user/test2")
    public User addUser2(@RequestBody User user) {
        System.err.println("addUser2");
       userCache.put(user.getId(), user);
       return user;
    }
    @GetMapping("/user/test3/{id}")
    public User getUser(@PathVariable Long id) {
        System.err.println("getUser");
        System.err.println(userCache.get(id).getPassword());
        return userCache.get(id);
    }


}
