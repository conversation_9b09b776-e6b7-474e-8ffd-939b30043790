server:
  port: 8080

spring:
  application:
    name: springboot-demo


# 添加日志配置
logging:
  level:
    org.springframework.cache: TRACE
    org.springframework.data.redis: DEBUG
    com.wangchao.springbootdemo: DEBUG
    com.alicp.jetcache: DEBUG

jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: caffeine #other choose：caffeine
      keyConvertor: jackson #other choose：fastjson/jackson
      limit: 100
  remote:
    default:
      type: redis
      keyConvertor: jackson #other choose：fastjson/jackson
      broadcastChannel: projectA
      valueEncoder: java #other choose：kryo/kryo5
      valueDecoder: java #other choose：kryo/kryo5
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: redis-18839.crce178.ap-east-1-1.ec2.redns.redis-cloud.com
      port: 18839
      password: dX77ahFSVZ6F2z7J2Wq00otin49tUX3K


