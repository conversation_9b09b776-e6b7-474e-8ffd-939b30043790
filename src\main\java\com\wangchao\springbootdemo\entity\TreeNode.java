package com.wangchao.springbootdemo.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 树形结构节点实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@ToString
public class TreeNode {
    
    /**
     * 节点ID
     */
    private Long id;
    
    /**
     * 父节点ID
     */
    private Long parentId;
    
    /**
     * 节点名称
     */
    private String name;
    
    /**
     * 子节点列表
     */
    private List<TreeNode> children = new ArrayList<>();
    
    /**
     * 带参数构造函数（不包含children）
     */
    public TreeNode(Long id, Long parentId, String name) {
        this.id = id;
        this.parentId = parentId;
        this.name = name;
    }
    
    /**
     * 添加子节点
     */
    public void addChild(TreeNode child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
    }
} 